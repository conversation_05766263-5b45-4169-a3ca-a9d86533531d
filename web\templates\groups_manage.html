<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8" x-data="groupsManager()">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">提供商分组管理</h1>
                <p class="text-gray-600">管理API提供商分组配置</p>
            </div>
            <div class="flex space-x-4">
                <a href="/" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    返回首页
                </a>
                <a href="/dashboard" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    返回仪表板
                </a>
                <button @click="showCreateModal = true" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    添加分组
                </button>
                <button @click="refreshGroups()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    刷新
                </button>
            </div>
        </div>

        <!-- Groups List -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-6">当前分组</h2>
            
            <div x-show="loading" class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">加载中...</p>
            </div>

            <div x-show="!loading && Object.keys(groups).length === 0" class="text-center py-8 text-gray-500">
                <p>暂无分组配置</p>
            </div>

            <div x-show="!loading && Object.keys(groups).length > 0" class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left">分组ID</th>
                            <th class="px-4 py-2 text-left">名称</th>
                            <th class="px-4 py-2 text-left">类型</th>
                            <th class="px-4 py-2 text-left">状态</th>
                            <th class="px-4 py-2 text-left">密钥数量</th>
                            <th class="px-4 py-2 text-left">健康状态</th>
                            <th class="px-4 py-2 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="(group, groupId) in groups" :key="groupId">
                            <tr class="border-t hover:bg-gray-50">
                                <td class="px-4 py-2 font-mono text-sm" x-text="groupId"></td>
                                <td class="px-4 py-2" x-text="group.group_name"></td>
                                <td class="px-4 py-2">
                                    <span class="px-2 py-1 rounded text-xs" :class="getProviderTypeClass(group.provider_type)" x-text="group.provider_type.toUpperCase()"></span>
                                </td>
                                <td class="px-4 py-2">
                                    <span class="px-2 py-1 rounded text-xs" :class="group.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="group.enabled ? '启用' : '禁用'"></span>
                                </td>
                                <td class="px-4 py-2" x-text="group.api_keys ? group.api_keys.length : 0"></td>
                                <td class="px-4 py-2">
                                    <span class="px-2 py-1 rounded text-xs" :class="group.healthy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="group.healthy ? '健康' : '异常'"></span>
                                </td>
                                <td class="px-4 py-2">
                                    <div class="flex space-x-2">
                                        <button @click="editGroup(groupId, group)" class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs">
                                            编辑
                                        </button>
                                        <button @click="toggleGroup(groupId)" class="px-2 py-1 rounded text-xs" :class="group.enabled ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : 'bg-green-500 hover:bg-green-600 text-white'" x-text="group.enabled ? '禁用' : '启用'">
                                        </button>
                                        <button @click="deleteGroup(groupId)" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <div x-show="showCreateModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click.self="closeModal()">
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col">
                <!-- 头部 -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900" x-text="showCreateModal ? '添加提供商分组' : '编辑提供商分组'"></h3>
                            <p class="text-sm text-gray-500" x-text="showCreateModal ? '配置新的API提供商分组' : '修改现有提供商分组配置'"></p>
                        </div>
                    </div>
                    <button @click="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 内容区域 -->
                <div class="flex-1 overflow-y-auto p-6">

                    <form id="groupForm" @submit.prevent="submitForm()" class="space-y-8">
                        <!-- 基本信息区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">基本信息</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">分组ID *</label>
                                    <input type="text" x-model="formData.group_id" :disabled="showEditModal" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: openai-group">
                                    <p class="text-xs text-gray-500 mt-1">唯一标识符，创建后不可修改</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">分组名称 *</label>
                                    <input type="text" x-model="formData.name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: OpenAI 官方">
                                    <p class="text-xs text-gray-500 mt-1">用于显示的友好名称</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 提供商配置区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">提供商配置</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">提供商类型 *</label>
                                    <select x-model="formData.provider_type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                                        <option value="">请选择提供商类型</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="azure_openai">Azure OpenAI</option>
                                        <option value="anthropic">Anthropic</option>
                                        <option value="gemini">Google Gemini</option>
                                        <option value="openrouter">OpenRouter</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">选择API提供商类型</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Base URL *</label>
                                    <input type="url" x-model="formData.base_url" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required placeholder="如: https://api.openai.com/v1">
                                    <p class="text-xs text-gray-500 mt-1">API服务的基础URL地址</p>
                                </div>

                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="checkbox" x-model="formData.enabled" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="text-sm text-gray-700">启用此分组</span>
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">禁用后该分组将不会处理请求</p>
                                </div>
                            </div>
                        </div>

                        <!-- 配置参数区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                </svg>
                                <h4 class="text-lg font-semibold text-gray-900">配置参数</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">超时时间（秒）</label>
                                    <input type="number" x-model="formData.timeout" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="1" max="600" placeholder="30">
                                    <p class="text-xs text-gray-500 mt-1">请求超时时间，建议30-120秒</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">最大重试次数</label>
                                    <input type="number" x-model="formData.max_retries" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="0" max="10" placeholder="3">
                                    <p class="text-xs text-gray-500 mt-1">失败后的重试次数，建议1-5次</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">轮换策略</label>
                                    <select x-model="formData.rotation_strategy" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="round_robin">轮询 (Round Robin)</option>
                                        <option value="random">随机 (Random)</option>
                                        <option value="least_used">最少使用 (Least Used)</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">多个密钥时的选择策略</p>
                                </div>
                            </div>
                        </div>

                        <!-- API密钥区域 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2h4zm-6 2v6h4V9H9z"></path>
                                    </svg>
                                    <h4 class="text-lg font-semibold text-gray-900">API密钥管理</h4>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="showBatchAddModal = true" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        批量添加
                                    </button>
                                    <button type="button" @click="addApiKey()" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加密钥
                                    </button>
                                    <button type="button" @click="validateKeys()" :disabled="validatingKeys || formData.api_keys.filter(k => k.trim()).length === 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span x-show="!validatingKeys">验证密钥</span>
                                        <span x-show="validatingKeys">验证中...</span>
                                    </button>
                                    <button type="button" @click="deleteSelectedKeys()" x-show="selectedKeys.length > 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        删除选中 (<span x-text="selectedKeys.length"></span>)
                                    </button>
                                    <button type="button" @click="deleteInvalidKeys()" x-show="invalidKeyIndexes.length > 0" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        删除失效 (<span x-text="invalidKeyIndexes.length"></span>)
                                    </button>
                                </div>
                            </div>

                            <!-- 密钥列表 -->
                            <div class="border rounded-md max-h-60 overflow-y-auto">
                                <div class="space-y-1 p-2">
                                    <template x-for="(key, index) in paginatedKeys" :key="index + keyPageOffset">
                                        <div class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded" :class="getKeyValidationClass(index + keyPageOffset)">
                                            <input type="checkbox" :value="index + keyPageOffset" x-model="selectedKeys" class="rounded">
                                            <div class="flex-1 relative">
                                                <input type="text" x-model="formData.api_keys[index + keyPageOffset]" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="输入API密钥">
                                                <!-- 验证状态指示器 -->
                                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'valid'" class="w-2 h-2 bg-green-500 rounded-full" title="密钥有效"></div>
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'invalid'" class="w-2 h-2 bg-red-500 rounded-full" title="密钥无效"></div>
                                                    <div x-show="keyValidationStatus[index + keyPageOffset] === 'validating'" class="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" title="验证中"></div>
                                                </div>
                                            </div>
                                            <button type="button" @click="removeApiKey(index + keyPageOffset)" class="text-red-500 hover:text-red-700 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </template>
                                </div>

                                <!-- 分页控制 -->
                                <div x-show="formData.api_keys.length > keysPerPage" class="border-t p-2 flex justify-between items-center bg-gray-50">
                                    <div class="text-sm text-gray-600">
                                        显示 <span x-text="keyPageOffset + 1"></span>-<span x-text="Math.min(keyPageOffset + keysPerPage, formData.api_keys.length)"></span>
                                        共 <span x-text="formData.api_keys.length"></span> 个密钥
                                    </div>
                                    <div class="flex space-x-1">
                                        <button type="button" @click="keyPage = Math.max(1, keyPage - 1)" :disabled="keyPage <= 1" class="px-2 py-1 text-sm border rounded disabled:opacity-50">
                                            上一页
                                        </button>
                                        <button type="button" @click="keyPage = Math.min(totalKeyPages, keyPage + 1)" :disabled="keyPage >= totalKeyPages" class="px-2 py-1 text-sm border rounded disabled:opacity-50">
                                            下一页
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 支持的模型 -->
                        <div class="md:col-span-2 mt-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-semibold">支持的模型（可选）</h4>
                                <div class="flex space-x-2">
                                    <button type="button" @click="loadAvailableModels()" :disabled="!formData.provider_type || !formData.base_url || formData.api_keys.filter(k => k.trim()).length === 0" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50">
                                        <span x-show="!loadingModels">加载模型</span>
                                        <span x-show="loadingModels">加载中...</span>
                                    </button>
                                    <button type="button" @click="selectAllModels()" x-show="availableModels.length > 0" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                        全选
                                    </button>
                                    <button type="button" @click="clearAllModels()" x-show="formData.models.length > 0" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                        清空
                                    </button>
                                </div>
                            </div>

                            <!-- 模型选择器 -->
                            <div x-show="availableModels.length > 0" class="border rounded-md p-3 bg-gray-50 mb-3">
                                <div class="flex justify-between items-center mb-2">
                                    <div class="text-sm text-gray-600">从API加载的可用模型：</div>
                                    <div class="text-xs text-gray-500">
                                        显示 <span x-text="filteredModels.length"></span> / <span x-text="availableModels.length"></span> 个模型
                                    </div>
                                </div>

                                <!-- 搜索框 -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="text" x-model="modelSearchQuery" @input="filterModels()" placeholder="搜索模型名称..." class="w-full px-3 py-2 pl-8 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <svg class="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        <button x-show="modelSearchQuery" @click="clearModelSearch()" class="absolute right-2 top-2 text-gray-400 hover:text-gray-600">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- 模型列表 -->
                                <div class="max-h-40 overflow-y-auto space-y-1">
                                    <template x-for="model in filteredModels" :key="model.id">
                                        <label class="flex items-center space-x-2 text-sm hover:bg-white p-1 rounded">
                                            <input type="checkbox" :value="model.id" x-model="formData.models" class="rounded">
                                            <span class="font-mono" x-text="model.id"></span>
                                            <span x-show="model.owned_by" class="text-gray-500" x-text="'(' + model.owned_by + ')'"></span>
                                        </label>
                                    </template>
                                    <div x-show="filteredModels.length === 0 && modelSearchQuery" class="text-center py-4 text-gray-500 text-sm">
                                        未找到匹配的模型
                                    </div>
                                </div>
                            </div>

                            <!-- 手动输入模型 -->
                            <div>
                                <div class="text-sm text-gray-600 mb-2">或手动输入模型名称：</div>
                                <textarea x-model="modelsText" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="每行一个模型名称，留空表示支持所有模型"></textarea>
                                <p class="text-sm text-gray-500 mt-1">每行输入一个模型名称，如：gpt-3.5-turbo</p>
                            </div>

                            <!-- 已选择的模型预览 -->
                            <div x-show="formData.models.length > 0" class="mt-3">
                                <div class="text-sm text-gray-600 mb-2">已选择的模型 (<span x-text="formData.models.length"></span>)：</div>
                                <div class="flex flex-wrap gap-1">
                                    <template x-for="model in formData.models" :key="model">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                            <span x-text="model"></span>
                                            <button type="button" @click="removeModel(model)" class="ml-1 text-blue-600 hover:text-blue-800">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 底部操作区域 -->
                <div class="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-lg">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            <span x-show="showCreateModal">填写完整信息后点击创建</span>
                            <span x-show="showEditModal">修改完成后点击更新保存</span>
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" @click="closeModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                取消
                            </button>
                            <button type="submit" form="groupForm" :disabled="submitting" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                <svg x-show="submitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-show="!submitting" x-text="showCreateModal ? '创建分组' : '更新分组'"></span>
                                <span x-show="submitting">处理中...</span>
                            </button>
                        </div>
                    </div>
                </div>
                    </form>
                </div>

                
            </div>
        </div>

        <!-- Batch Add Keys Modal -->
        <div x-show="showBatchAddModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
             @click.self="showBatchAddModal = false"
             style="display: none;"
             x-cloak>
            <div class="bg-white rounded-lg p-6 w-full max-w-lg">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">批量添加API密钥</h3>
                    <button @click="showBatchAddModal = false" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">API密钥列表</label>
                    <textarea x-model="batchKeysText" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="8" placeholder="每行输入一个API密钥&#10;支持以下格式：&#10;sk-1234567890abcdef&#10;your-api-key-here&#10;&#10;空行将被忽略"></textarea>
                    <p class="text-sm text-gray-500 mt-1">每行输入一个API密钥，空行将被自动忽略</p>
                </div>

                <div class="flex justify-end space-x-4">
                    <button type="button" @click="showBatchAddModal = false" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" @click="addBatchKeys()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        添加密钥
                    </button>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div x-show="message" x-transition class="fixed top-4 right-4 z-50">
            <div class="px-4 py-2 rounded-md text-white" :class="messageType === 'success' ? 'bg-green-500' : 'bg-red-500'">
                <span x-text="message"></span>
            </div>
        </div>
    </div>

    <script>
        function groupsManager() {
            return {
                groups: {},
                loading: false,
                showCreateModal: false,
                showEditModal: false,
                showBatchAddModal: false,
                submitting: false,
                message: '',
                messageType: 'success',
                editingGroupId: '',

                // 密钥分页相关
                selectedKeys: [],
                keyPage: 1,
                keysPerPage: 5,
                batchKeysText: '',

                // 密钥验证相关
                validatingKeys: false,
                keyValidationStatus: {}, // 存储每个密钥的验证状态
                invalidKeyIndexes: [], // 存储无效密钥的索引

                // 模型相关
                availableModels: [],
                filteredModels: [],
                loadingModels: false,
                modelSearchQuery: '',

                formData: {
                    group_id: '',
                    name: '',
                    provider_type: '',
                    base_url: '',
                    enabled: true,
                    timeout: 30,
                    max_retries: 3,
                    rotation_strategy: 'round_robin',
                    api_keys: [''],
                    models: []
                },
                modelsText: '',

                // 计算属性
                get keyPageOffset() {
                    return (this.keyPage - 1) * this.keysPerPage;
                },

                get paginatedKeys() {
                    const start = this.keyPageOffset;
                    const end = start + this.keysPerPage;
                    return this.formData.api_keys.slice(start, end);
                },

                get totalKeyPages() {
                    return Math.ceil(this.formData.api_keys.length / this.keysPerPage);
                },

                async init() {
                    await this.refreshGroups();
                },

                async refreshGroups() {
                    this.loading = true;
                    try {
                        const response = await fetch('/admin/groups/manage');
                        if (response.ok) {
                            const data = await response.json();
                            this.groups = data.groups || {};
                        } else {
                            this.showMessage('加载分组失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                editGroup(groupId, group) {
                    this.editingGroupId = groupId;
                    this.formData = {
                        group_id: groupId,
                        name: group.group_name,
                        provider_type: group.provider_type,
                        base_url: group.base_url,
                        enabled: group.enabled,
                        timeout: group.timeout || 30,
                        max_retries: group.max_retries || 3,
                        rotation_strategy: group.rotation_strategy || 'round_robin',
                        api_keys: group.api_keys && group.api_keys.length > 0 ? [...group.api_keys] : [''],
                        models: group.models || []
                    };
                    this.modelsText = (group.models || []).join('\n');
                    this.selectedKeys = [];
                    this.keyPage = 1;
                    this.availableModels = [];
                    this.filteredModels = [];
                    this.modelSearchQuery = '';
                    this.keyValidationStatus = {};
                    this.invalidKeyIndexes = [];
                    this.showEditModal = true;
                },

                async submitForm() {
                    this.submitting = true;
                    try {
                        // 处理模型列表 - 合并选择的模型和手动输入的模型
                        const manualModels = this.modelsText.split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0);

                        // 去重合并模型列表
                        const allModels = [...new Set([...this.formData.models, ...manualModels])];
                        this.formData.models = allModels;

                        // 过滤空的API密钥
                        this.formData.api_keys = this.formData.api_keys.filter(key => key.trim().length > 0);

                        const url = this.showCreateModal ? '/admin/groups' : `/admin/groups/${this.editingGroupId}`;
                        const method = this.showCreateModal ? 'POST' : 'PUT';

                        const response = await fetch(url, {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.formData)
                        });

                        const data = await response.json();
                        
                        if (response.ok) {
                            this.showMessage(data.message, 'success');
                            this.closeModal();
                            await this.refreshGroups();
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.submitting = false;
                    }
                },

                async toggleGroup(groupId) {
                    try {
                        const response = await fetch(`/admin/groups/${groupId}/toggle`, {
                            method: 'POST'
                        });

                        const data = await response.json();
                        
                        if (response.ok) {
                            this.showMessage(data.message, 'success');
                            await this.refreshGroups();
                        } else {
                            this.showMessage(data.message || '操作失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    }
                },

                async deleteGroup(groupId) {
                    if (!confirm('确定要删除这个分组吗？此操作不可恢复。')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/groups/${groupId}`, {
                            method: 'DELETE'
                        });

                        const data = await response.json();
                        
                        if (response.ok) {
                            this.showMessage(data.message, 'success');
                            await this.refreshGroups();
                        } else {
                            this.showMessage(data.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    }
                },

                closeModal() {
                    this.showCreateModal = false;
                    this.showEditModal = false;
                    this.showBatchAddModal = false;
                    this.editingGroupId = '';
                    this.resetForm();
                },

                resetForm() {
                    this.formData = {
                        group_id: '',
                        name: '',
                        provider_type: '',
                        base_url: '',
                        enabled: true,
                        timeout: 30,
                        max_retries: 3,
                        rotation_strategy: 'round_robin',
                        api_keys: [''],
                        models: []
                    };
                    this.modelsText = '';
                    this.selectedKeys = [];
                    this.keyPage = 1;
                    this.availableModels = [];
                    this.filteredModels = [];
                    this.modelSearchQuery = '';
                    this.batchKeysText = '';
                    this.keyValidationStatus = {};
                    this.invalidKeyIndexes = [];
                },

                addApiKey() {
                    this.formData.api_keys.push('');
                },

                removeApiKey(index) {
                    if (this.formData.api_keys.length > 1) {
                        this.formData.api_keys.splice(index, 1);
                        // 调整页码如果当前页没有数据了
                        if (this.keyPageOffset >= this.formData.api_keys.length && this.keyPage > 1) {
                            this.keyPage--;
                        }
                        // 清除选中状态
                        this.selectedKeys = this.selectedKeys.filter(i => i < this.formData.api_keys.length);
                    }
                },

                // 批量添加密钥
                addBatchKeys() {
                    const keys = this.batchKeysText.split('\n')
                        .map(line => line.trim())
                        .filter(line => line.length > 0);

                    if (keys.length > 0) {
                        // 移除空的密钥项
                        this.formData.api_keys = this.formData.api_keys.filter(key => key.trim().length > 0);
                        // 添加新密钥
                        this.formData.api_keys.push(...keys);
                        // 如果原来没有密钥，确保至少有一个空项
                        if (this.formData.api_keys.length === 0) {
                            this.formData.api_keys.push('');
                        }
                        this.batchKeysText = '';
                        this.showBatchAddModal = false;
                        this.showMessage(`成功添加 ${keys.length} 个密钥`, 'success');
                    }
                },

                // 删除选中的密钥
                deleteSelectedKeys() {
                    if (this.selectedKeys.length === 0) return;

                    if (!confirm(`确定要删除选中的 ${this.selectedKeys.length} 个密钥吗？`)) {
                        return;
                    }

                    // 按索引倒序删除，避免索引变化问题
                    const sortedIndexes = [...this.selectedKeys].sort((a, b) => b - a);
                    sortedIndexes.forEach(index => {
                        this.formData.api_keys.splice(index, 1);
                    });

                    // 确保至少有一个空项
                    if (this.formData.api_keys.length === 0) {
                        this.formData.api_keys.push('');
                    }

                    // 清除选中状态
                    this.selectedKeys = [];

                    // 调整页码
                    if (this.keyPageOffset >= this.formData.api_keys.length && this.keyPage > 1) {
                        this.keyPage--;
                    }

                    this.showMessage(`成功删除 ${sortedIndexes.length} 个密钥`, 'success');
                },

                // 加载可用模型
                async loadAvailableModels() {
                    // 如果是编辑模式，可以直接从现有分组加载模型
                    if (this.showEditModal && this.editingGroupId) {
                        this.loadingModels = true;
                        try {
                            const response = await fetch(`/admin/models/available/${this.editingGroupId}`);

                            if (response.ok) {
                                const data = await response.json();
                                // 解析响应数据
                                const groupData = data.data[this.editingGroupId];
                                if (groupData && groupData.models && groupData.models.data) {
                                    this.availableModels = groupData.models.data;
                                    this.filterModels();
                                    this.showMessage(`成功加载 ${this.availableModels.length} 个模型`, 'success');
                                } else {
                                    this.availableModels = [];
                                    this.filterModels();
                                    this.showMessage('未找到可用模型', 'error');
                                }
                            } else {
                                const errorData = await response.json();
                                this.showMessage('加载模型失败: ' + (errorData.error?.message || '未知错误'), 'error');
                            }
                        } catch (error) {
                            this.showMessage('网络错误: ' + error.message, 'error');
                        } finally {
                            this.loadingModels = false;
                        }
                        return;
                    }

                    // 创建模式需要先验证配置
                    if (!this.formData.provider_type || !this.formData.base_url) {
                        this.showMessage('请先填写提供商类型和Base URL', 'error');
                        return;
                    }

                    const validKeys = this.formData.api_keys.filter(key => key.trim().length > 0);
                    if (validKeys.length === 0) {
                        this.showMessage('请先添加至少一个API密钥', 'error');
                        return;
                    }

                    this.loadingModels = true;
                    try {
                        // 创建临时分组配置来测试模型加载
                        const tempGroupData = {
                            name: this.formData.name || 'temp',
                            provider_type: this.formData.provider_type,
                            base_url: this.formData.base_url,
                            enabled: true,
                            timeout: this.formData.timeout || 30,
                            max_retries: this.formData.max_retries || 3,
                            rotation_strategy: this.formData.rotation_strategy || 'round_robin',
                            api_keys: validKeys
                        };

                        // 使用测试API来加载模型
                        const response = await fetch('/admin/models/test', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(tempGroupData)
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.models && data.models.data) {
                                this.availableModels = data.models.data;
                                this.filterModels();
                                this.showMessage(`成功加载 ${this.availableModels.length} 个模型`, 'success');
                            } else {
                                this.availableModels = [];
                                this.filterModels();
                                this.showMessage('未找到可用模型', 'error');
                            }
                        } else {
                            const errorData = await response.json();
                            this.showMessage('加载模型失败: ' + (errorData.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.loadingModels = false;
                    }
                },

                // 全选模型
                selectAllModels() {
                    this.formData.models = this.filteredModels.map(model => model.id);
                },

                // 清空模型选择
                clearAllModels() {
                    this.formData.models = [];
                },

                // 移除单个模型
                removeModel(modelId) {
                    this.formData.models = this.formData.models.filter(id => id !== modelId);
                },

                // 过滤模型列表
                filterModels() {
                    if (!this.modelSearchQuery.trim()) {
                        this.filteredModels = [...this.availableModels];
                    } else {
                        const query = this.modelSearchQuery.toLowerCase();
                        this.filteredModels = this.availableModels.filter(model =>
                            model.id.toLowerCase().includes(query) ||
                            (model.owned_by && model.owned_by.toLowerCase().includes(query))
                        );
                    }
                },

                // 清空搜索
                clearModelSearch() {
                    this.modelSearchQuery = '';
                    this.filterModels();
                },

                // 验证密钥有效性
                async validateKeys() {
                    if (!this.editingGroupId && (!this.formData.provider_type || !this.formData.base_url)) {
                        this.showMessage('请先填写提供商类型和Base URL', 'error');
                        return;
                    }

                    const validKeys = this.formData.api_keys.filter(key => key.trim().length > 0);
                    if (validKeys.length === 0) {
                        this.showMessage('没有可验证的密钥', 'error');
                        return;
                    }

                    this.validatingKeys = true;

                    // 重置验证状态
                    this.keyValidationStatus = {};
                    this.invalidKeyIndexes = [];

                    // 设置所有密钥为验证中状态
                    this.formData.api_keys.forEach((key, index) => {
                        if (key.trim()) {
                            this.keyValidationStatus[index] = 'validating';
                        }
                    });

                    try {
                        const groupId = this.editingGroupId || 'temp';
                        const response = await fetch(`/admin/keys/validate/${groupId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                api_keys: this.formData.api_keys
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();

                            // 更新验证状态
                            data.results.forEach(result => {
                                this.keyValidationStatus[result.index] = result.valid ? 'valid' : 'invalid';
                                if (!result.valid) {
                                    this.invalidKeyIndexes.push(result.index);
                                }
                            });

                            this.showMessage(
                                `密钥验证完成：${data.valid_keys} 个有效，${data.invalid_keys} 个无效` +
                                (data.test_model ? `（使用模型：${data.test_model}）` : ''),
                                data.invalid_keys > 0 ? 'warning' : 'success'
                            );
                        } else {
                            const errorData = await response.json();
                            this.showMessage('验证失败: ' + (errorData.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        this.showMessage('网络错误: ' + error.message, 'error');
                    } finally {
                        this.validatingKeys = false;
                    }
                },

                // 删除无效密钥
                deleteInvalidKeys() {
                    if (this.invalidKeyIndexes.length === 0) return;

                    if (!confirm(`确定要删除 ${this.invalidKeyIndexes.length} 个无效密钥吗？`)) {
                        return;
                    }

                    // 按索引倒序删除，避免索引变化问题
                    const sortedIndexes = [...this.invalidKeyIndexes].sort((a, b) => b - a);
                    sortedIndexes.forEach(index => {
                        this.formData.api_keys.splice(index, 1);
                        delete this.keyValidationStatus[index];
                    });

                    // 确保至少有一个空项
                    if (this.formData.api_keys.length === 0) {
                        this.formData.api_keys.push('');
                    }

                    // 重新索引验证状态
                    const newValidationStatus = {};
                    const newInvalidIndexes = [];
                    let newIndex = 0;

                    for (let oldIndex = 0; oldIndex < Object.keys(this.keyValidationStatus).length + sortedIndexes.length; oldIndex++) {
                        if (!sortedIndexes.includes(oldIndex)) {
                            if (this.keyValidationStatus[oldIndex]) {
                                newValidationStatus[newIndex] = this.keyValidationStatus[oldIndex];
                                if (this.keyValidationStatus[oldIndex] === 'invalid') {
                                    newInvalidIndexes.push(newIndex);
                                }
                            }
                            newIndex++;
                        }
                    }

                    this.keyValidationStatus = newValidationStatus;
                    this.invalidKeyIndexes = newInvalidIndexes;

                    // 清除选中状态
                    this.selectedKeys = this.selectedKeys.filter(i => i < this.formData.api_keys.length);

                    // 调整页码
                    if (this.keyPageOffset >= this.formData.api_keys.length && this.keyPage > 1) {
                        this.keyPage--;
                    }

                    this.showMessage(`成功删除 ${sortedIndexes.length} 个无效密钥`, 'success');
                },

                // 获取密钥验证样式类
                getKeyValidationClass(index) {
                    const status = this.keyValidationStatus[index];
                    switch (status) {
                        case 'valid':
                            return 'bg-green-50 border-green-200';
                        case 'invalid':
                            return 'bg-red-50 border-red-200';
                        case 'validating':
                            return 'bg-yellow-50 border-yellow-200';
                        default:
                            return '';
                    }
                },

                getProviderTypeClass(type) {
                    const typeClasses = {
                        'openai': 'bg-blue-100 text-blue-800',
                        'gemini': 'bg-green-100 text-green-800',
                        'anthropic': 'bg-purple-100 text-purple-800',
                        'azure_openai': 'bg-cyan-100 text-cyan-800'
                    };
                    return typeClasses[type] || 'bg-gray-100 text-gray-800';
                },

                showMessage(msg, type = 'success') {
                    this.message = msg;
                    this.messageType = type;
                    setTimeout(() => {
                        this.message = '';
                    }, 5000);
                }
            }
        }
    </script>
</body>
</html>
