# Git
.git
.gitignore

# Documentation
README.md
DEPLOYMENT.md
*.md

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Build artifacts
turnsapi.exe
turnsapi_amd64_x86
*.exe

# Test files
test_api.ps1
start.ps1

# Docker files (avoid recursive copying)
Dockerfile
docker-compose.yml
.dockerignore

# Config files (will be mounted separately)
config/config.yaml
